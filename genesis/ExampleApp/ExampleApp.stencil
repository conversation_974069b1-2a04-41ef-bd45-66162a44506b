import SwiftUI
{% if type == 'ChatRed' or type == 'ScreenShare' %}
import CCAIKit
import CCA<PERSON>hat
import <PERSON><PERSON><PERSON>hatRed
{% endif %}
{% if type == 'ScreenShare' %}
import CCAIScreenShare
{% endif %}

struct ContentView: View {
    var body: some View {
        VStack {
            Text("Hello, world!")
        }
        .padding()
    }
}

@main
struct ExampleApp: App {
    var body: some Scene {
        WindowGroup {
            ContentView()
        }
    }
}

#Preview {
    ContentView()
}
